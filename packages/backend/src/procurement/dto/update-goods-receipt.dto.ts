import { PartialType } from '@nestjs/mapped-types';
import { IsOptional, IsEnum, IsDateString, IsString, IsInt, Min } from 'class-validator';
import { Transform } from 'class-transformer';
import { GoodsReceiptStatus, QualityControlStatus } from '@prisma/client';
import { CreateGoodsReceiptDto } from './create-goods-receipt.dto';

export class UpdateGoodsReceiptItemDto {
  @IsOptional()
  @IsString()
  id?: string; // For updating existing items

  @IsOptional()
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  @IsInt()
  @Min(0)
  quantityAccepted?: number;

  @IsOptional()
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  @IsInt()
  @Min(0)
  quantityRejected?: number;

  @IsOptional()
  @IsEnum(QualityControlStatus)
  qualityStatus?: QualityControlStatus;

  @IsOptional()
  @IsString()
  qualityNotes?: string;

  @IsOptional()
  @IsDateString()
  inspectionDate?: string;

  @IsOptional()
  @IsString()
  inspectionBy?: string;

  @IsOptional()
  @IsString()
  storageLocation?: string;

  @IsOptional()
  @IsString()
  conditionOnReceipt?: string;

  @IsOptional()
  @IsString()
  damageNotes?: string;

  // Product Substitution Information
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  isSubstitution?: boolean;

  @IsOptional()
  @IsString()
  originalProductId?: string;

  @IsOptional()
  @IsString()
  substitutionReason?: string;

  @IsOptional()
  @IsString()
  substitutionApprovedBy?: string;

  @IsOptional()
  @IsDateString()
  substitutionApprovedAt?: string;

  @IsOptional()
  @IsString()
  substitutionNotes?: string;
}

export class UpdateGoodsReceiptDto extends PartialType(CreateGoodsReceiptDto) {
  @IsOptional()
  @IsEnum(GoodsReceiptStatus)
  status?: GoodsReceiptStatus;

  @IsOptional()
  @IsDateString()
  inspectionDate?: string;

  @IsOptional()
  @IsString()
  inspectionBy?: string;

  @IsOptional()
  @IsEnum(QualityControlStatus)
  qualityStatus?: QualityControlStatus;

  @IsOptional()
  @IsString()
  qualityNotes?: string;

  @IsOptional()
  @IsString()
  receivedBy?: string;

  @IsOptional()
  @IsString()
  deliveryCondition?: string;
}
